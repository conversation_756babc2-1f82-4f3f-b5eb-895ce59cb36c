import { NextResponse } from 'next/server'
import mysql from 'mysql2/promise'

// Create a connection pool
const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'goat_management',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  enableKeepAlive: true,
  keepAliveInitialDelay: 0
})

export async function GET(request) {
  let connection

  try {
    // Get query parameters
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '5')

    // Get a connection from the pool
    connection = await pool.getConnection()

    // Get recent report generations from the database
    const [reportGenerations] = await connection.execute(`
      SELECT
        id,
        report_type,
        date_range,
        generated_by,
        filters,
        file_name,
        file_format,
        generated_at,
        DATE_FORMAT(generated_at, '%Y-%m-%d %H:%i:%s') as formatted_date
      FROM report_generations
      ORDER BY generated_at DESC
      LIMIT ?
    `, [limit])

    // Format the reports for display
    const formattedReports = reportGenerations.map((report, index) => {
      try {
        const reportTypeFormatted = report.report_type ?
          report.report_type.charAt(0).toUpperCase() + report.report_type.slice(1) :
          'Unknown'

        return {
          id: report.id,
          report_type: reportTypeFormatted,
          title: `${reportTypeFormatted} Report (${report.date_range || 'N/A'} days)`,
          summary: `Generated as ${report.file_format?.toUpperCase() || 'PDF'} by ${report.generated_by || 'Unknown'}`,
          date: report.formatted_date,
          relative_date: report.generated_at ? getRelativeDate(new Date(report.generated_at)) : 'Unknown',
          file_name: report.file_name,
          file_format: report.file_format,
          tab_key: report.report_type ? report.report_type.toLowerCase() : 'unknown',
          icon: getIconForReportType(report.report_type || 'unknown'),
          color: getColorForReportType(report.report_type || 'unknown'),
          downloadable: true
        }

      } catch (formatError) {
        console.error(`Error formatting report ${index + 1}:`, formatError)
        return {
          id: report.id || 0,
          report_type: 'Error',
          title: 'Error formatting report',
          summary: 'Error occurred while formatting this report',
          date: 'Unknown',
          relative_date: 'Unknown',
          file_name: 'Unknown',
          file_format: 'unknown',
          tab_key: 'error',
          icon: 'LucideAlertCircle',
          color: 'text-red-600',
          downloadable: false
        }
      }
    })

    return NextResponse.json({
      success: true,
      reports: formattedReports,
      count: formattedReports.length
    })

  } catch (error) {
    console.error('Error in recent reports API:', error)
    return NextResponse.json(
      {
        success: false,
        error: error.message,
        stack: error.stack
      },
      { status: 500 }
    )
  } finally {
    if (connection) {
      connection.release()
    }
  }
}

// Helper function to get relative date
function getRelativeDate(date) {
  try {
    if (!date || isNaN(date.getTime())) {
      return 'Unknown'
    }

    const now = new Date()
    const diffTime = Math.abs(now - date)
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 0) {
      return 'Today'
    } else if (diffDays === 1) {
      return 'Yesterday'
    } else if (diffDays <= 7) {
      return `${diffDays} days ago`
    } else if (diffDays <= 30) {
      const weeks = Math.floor(diffDays / 7)
      return `${weeks} week${weeks > 1 ? 's' : ''} ago`
    } else if (diffDays <= 365) {
      const months = Math.floor(diffDays / 30)
      return `${months} month${months > 1 ? 's' : ''} ago`
    } else {
      const years = Math.floor(diffDays / 365)
      return `${years} year${years > 1 ? 's' : ''} ago`
    }
  } catch (error) {
    console.error('Error calculating relative date:', error)
    return 'Unknown'
  }
}

// Helper function to get icon for report type
function getIconForReportType(reportType) {
  switch (reportType.toLowerCase()) {
    case 'financial':
      return 'LucideCoins'
    case 'health':
      return 'LucideHeart'
    case 'breeding':
      return 'LucideCalendarClock'
    case 'inventory':
      return 'LucidePackage'
    default:
      return 'LucideBarChart2'
  }
}

// Helper function to get color for report type
function getColorForReportType(reportType) {
  switch (reportType.toLowerCase()) {
    case 'financial':
      return 'text-green-600'
    case 'health':
      return 'text-red-600'
    case 'breeding':
      return 'text-blue-600'
    case 'inventory':
      return 'text-purple-600'
    default:
      return 'text-gray-600'
  }
}

// POST - Save a report generation record
export async function POST(request) {
  let connection

  try {
    const body = await request.json()
    const { reportType, dateRange, generatedBy, filters } = body

    // Get a connection from the pool
    connection = await pool.getConnection()

    // Generate file name
    const currentDate = new Date().toISOString().split('T')[0]
    const reportTypeFormatted = reportType.charAt(0).toUpperCase() + reportType.slice(1)
    const format = filters?.format || 'pdf'
    const fileName = `${reportTypeFormatted}_Report_${currentDate}.${format}`

    // Insert report generation record
    const [result] = await connection.execute(`
      INSERT INTO report_generations (
        report_type,
        date_range,
        generated_by,
        filters,
        file_name,
        file_format,
        generated_at
      ) VALUES (?, ?, ?, ?, ?, ?, NOW())
    `, [
      reportType,
      dateRange,
      generatedBy || 'User',
      JSON.stringify(filters || {}),
      fileName,
      format
    ])

    return NextResponse.json({
      success: true,
      reportId: result.insertId,
      fileName: fileName,
      message: 'Report generation recorded'
    })

  } catch (error) {
    console.error('Error recording report generation:', error)
    return NextResponse.json(
      { error: 'Failed to record report generation: ' + error.message },
      { status: 500 }
    )
  } finally {
    if (connection) connection.release()
  }
}
